// Ultra-fast Publications Filter - Optimized for speed
// Minified and optimized version for better performance

(function() {
    'use strict';
    
    // Minimal data structure - only what's needed for filtering
    const papers = [
        {t:"PolarFree: Polarization-based Reflection-Free Imaging",a:["<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON> Li","<PERSON><PERSON><PERSON>","Jinwei Gu"],v:"CVPR",y:2025,tp:"conference",tg:["Computational Photography","Polarization"],f:1,l:{pdf:"https://mdyao.github.io/PolarFree/",code:"https://github.com/mdyao/PolarFree"},i:"papers/2025/PolarFree.png"},
        {t:"Uni-ISP: Unifying the Learning of ISPs from Multiple Cameras",a:["Ling<PERSON> Li","Ming<PERSON> Yao","Xingyu Meng","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","Jinwei Gu"],v:"arXiv",y:2024,tp:"preprint",tg:["ISP","Multi-Camera"],l:{pdf:"https://arxiv.org/abs/2406.01003"},i:"papers/2025/Uni-ISP.png"},
        {t:"Continuous Spatial-Spectral Reconstruction via Implicit Neural Representation",a:["Ruikang Xu","Mingde Yao","Chang Chen","Lizhi Wang","Zhiwei Xiong"],v:"IJCV",y:2024,tp:"journal",tg:["Neural Representation","Super-Resolution"],aw:"Best Paper Honorable Mention (ECCVW 2022)",l:{pdf:"https://link.springer.com/article/10.1007/s11263-024-02150-3"},i:"papers/2024/NeSR.png"},
        {t:"Neural Degradation Representation Learning for All-In-One Image Restoration",a:["Mingde Yao","Ruikang Xu","Yuanshen Guan","Jie Huang","Zhiwei Xiong"],v:"T-IP",y:2024,tp:"journal",tg:["Image Restoration","Deep Learning"],l:{pdf:"https://arxiv.org/abs/2310.12848"},i:"papers/2023/DR-Restore.png"},
        {t:"Generalized Lightness Adaptation with Channel Selective Normalization",a:["Mingde Yao","Jie Huang","Xin Jin","Ruikang Xu","Shenglong Zhou","Man Zhou","Zhiwei Xiong"],v:"ICCV",y:2023,tp:"conference",tg:["Lightness Adaptation","Normalization"],l:{pdf:"https://openaccess.thecvf.com/content/ICCV2023/papers/Yao_Generalized_Lightness_Adaptation_with_Channel_Selective_Normalization_ICCV_2023_paper.pdf"},i:"papers/2023/CSN.png"}
    ];
    
    // Fast filter state
    let f = {s:'',y:[],v:[],tp:[],f:0,aw:0};
    
    // Cache DOM elements
    let els = {};
    
    // Initialize
    function init() {
        // Cache frequently used elements
        els.search = document.getElementById('search-input');
        els.results = document.getElementById('results-count');
        els.list = document.getElementById('publications-list');
        els.yearFilters = document.getElementById('year-filters');
        els.venueFilters = document.getElementById('venue-filters');
        els.typeFilters = document.getElementById('type-filters');
        els.featuredBtn = document.getElementById('featured-btn');
        els.awardBtn = document.getElementById('award-btn');
        els.clearBtn = document.getElementById('clear-btn');
        
        createFilters();
        render();
        setupEvents();
    }
    
    // Create filter buttons with minimal DOM manipulation
    function createFilters() {
        const years = [...new Set(papers.map(p => p.y))].sort((a,b) => b-a);
        const venues = [...new Set(papers.map(p => p.v))].sort();
        const types = [...new Set(papers.map(p => p.tp))].sort();
        
        els.yearFilters.innerHTML = years.map(y => `<button class="filter-btn" data-type="y" data-value="${y}">${y}</button>`).join('');
        els.venueFilters.innerHTML = venues.map(v => `<button class="filter-btn" data-type="v" data-value="${v}">${v}</button>`).join('');
        els.typeFilters.innerHTML = types.map(t => `<button class="filter-btn" data-type="tp" data-value="${t}">${t}</button>`).join('');
    }
    
    // Event delegation for better performance
    function setupEvents() {
        // Search with debouncing
        let searchTimeout;
        els.search.oninput = (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                f.s = e.target.value.toLowerCase();
                render();
            }, 150);
        };
        
        // Filter buttons with event delegation
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('filter-btn') && e.target.dataset.type) {
                toggleFilter(e.target.dataset.type, e.target.dataset.value, e.target);
            }
        });
        
        els.featuredBtn.onclick = () => {
            f.f = !f.f;
            els.featuredBtn.classList.toggle('active');
            render();
        };
        
        els.awardBtn.onclick = () => {
            f.aw = !f.aw;
            els.awardBtn.classList.toggle('active');
            render();
        };
        
        els.clearBtn.onclick = () => {
            f = {s:'',y:[],v:[],tp:[],f:0,aw:0};
            els.search.value = '';
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            render();
        };
    }
    
    function toggleFilter(type, value, btn) {
        const val = type === 'y' ? parseInt(value) : value;
        const idx = f[type].indexOf(val);
        
        if (idx > -1) {
            f[type].splice(idx, 1);
            btn.classList.remove('active');
        } else {
            f[type].push(val);
            btn.classList.add('active');
        }
        render();
    }
    
    // Optimized filtering with early returns
    function filterPapers() {
        return papers.filter(p => {
            if (f.s && ![p.t, ...p.a, p.v, ...p.tg].join(' ').toLowerCase().includes(f.s)) return false;
            if (f.y.length && !f.y.includes(p.y)) return false;
            if (f.v.length && !f.v.includes(p.v)) return false;
            if (f.tp.length && !f.tp.includes(p.tp)) return false;
            if (f.f && !p.f) return false;
            if (f.aw && !p.aw) return false;
            return true;
        });
    }
    
    // Fast rendering with template literals
    function render() {
        const filtered = filterPapers();
        els.results.textContent = `${filtered.length} publication${filtered.length !== 1 ? 's' : ''} found`;
        
        if (filtered.length === 0) {
            els.list.innerHTML = '<div style="text-align:center;padding:40px;color:#666;">No publications found</div>';
            return;
        }
        
        // Use DocumentFragment for better performance
        const fragment = document.createDocumentFragment();
        
        filtered.forEach(p => {
            const div = document.createElement('div');
            div.style.cssText = 'display:flex;gap:20px;background:white;padding:20px;margin:15px 0;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1)';
            
            div.innerHTML = `
                <img src="${p.i}" alt="${p.t}" style="width:200px;height:120px;object-fit:cover;border-radius:6px" loading="lazy">
                <div style="flex:1">
                    <h3 style="margin:0 0 10px 0;color:#333">
                        ${p.t}
                        ${p.f ? '<span style="background:#d4edda;color:#155724;padding:2px 6px;border-radius:3px;font-size:11px;margin-left:8px">⭐ Featured</span>' : ''}
                    </h3>
                    ${p.aw ? `<div style="background:#fff3cd;color:#856404;padding:4px 8px;border-radius:4px;font-size:12px;margin-bottom:8px;display:inline-block">🏆 ${p.aw}</div>` : ''}
                    <p style="margin:0 0 8px 0;color:#666">
                        ${p.a.map(author => author === 'Mingde Yao' ? `<strong style="color:#d32f2f">${author}</strong>` : author).join(', ')}
                    </p>
                    <p style="margin:0 0 10px 0;color:#555">
                        <span style="background:${getVenueColor(p.tp)};padding:2px 6px;border-radius:3px;font-size:12px;margin-right:8px">${p.v}</span>
                        ${p.y}
                    </p>
                    <div>
                        ${Object.entries(p.l).map(([type, url]) => 
                            `<a href="${url}" target="_blank" style="color:#043d98;text-decoration:none;margin-right:12px;font-size:14px">${type.toUpperCase()}</a>`
                        ).join('')}
                    </div>
                </div>
            `;
            
            fragment.appendChild(div);
        });
        
        els.list.innerHTML = '';
        els.list.appendChild(fragment);
    }
    
    function getVenueColor(type) {
        const colors = {
            conference: '#ffebee;color:#c62828',
            journal: '#e3f2fd;color:#1565c0',
            preprint: '#fff3e0;color:#ef6c00'
        };
        return colors[type] || '#f5f5f5;color:#666';
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // Expose for external access
    window.FastFilter = { render, filterPapers };
})();
